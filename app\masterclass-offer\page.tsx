"use client";

import React, { useEffect, useState } from "react";
import Head from "next/head";

const PRICE = 500;
const CHECKOUT_URL = "/checkout?product=masterclass&price=500"; // replace with your Stripe/ThriveCart URL

function useEvergreenCountdown(key = "mc_offer_expiry", hours = 24) {
  const [timeLeft, setTimeLeft] = useState(() => {
    if (typeof window === "undefined") return hours * 60 * 60 * 1000;
    const raw = sessionStorage.getItem(key);
    if (raw) return Math.max(0, Number(raw) - Date.now());
    const expiry = Date.now() + hours * 60 * 60 * 1000;
    sessionStorage.setItem(key, String(expiry));
    return expiry - Date.now();
  });

  useEffect(() => {
    const iv = setInterval(() => {
      const raw = sessionStorage.getItem(key);
      const remaining = raw ? Math.max(0, Number(raw) - Date.now()) : 0;
      setTimeLeft(remaining);
    }, 1000);
    return () => clearInterval(iv);
  }, [key]);

  const formatted = () => {
    const s = Math.max(0, Math.floor(timeLeft / 1000));
    const h = Math.floor(s / 3600);
    const m = Math.floor((s % 3600) / 60);
    const sec = s % 60;
    return `${h.toString().padStart(2, "0")}:${m.toString().padStart(2, "0")}:${sec
      .toString()
      .padStart(2, "0")}`;
  };

  return { timeLeft, formatted };
}

export default function MasterclassOfferPage() {
  const { formatted } = useEvergreenCountdown();
  const [showSticky, setShowSticky] = useState(false);

  useEffect(() => {
    const onScroll = () => setShowSticky(window.scrollY > 220);
    onScroll();
    window.addEventListener("scroll", onScroll, { passive: true });
    return () => window.removeEventListener("scroll", onScroll);
  }, []);

  const sendEvent = (name: string, payload: Record<string, any> = {}) => {
    try {
      if ((window as any).gtag) (window as any).gtag("event", name, payload);
      if ((window as any).dataLayer) (window as any).dataLayer.push({ event: name, ...payload });
    } catch (e) {
      // noop
    }
  };

  const goToCheckout = () => {
    sendEvent("start_checkout", { value: PRICE, currency: "USD", content_type: "product" });
    window.location.href = CHECKOUT_URL;
  };

  return (
    <>
      <Head>
        <title>Crypto Masterclass — Limited Offer</title>
        <meta name="robots" content="noindex" />
        <meta name="description" content="Plug this crypto masterclass into your trading to find high-probability setups, avoid big losses, and build real wealth." />
      </Head>

      <div className="min-h-screen bg-gray-900 text-white antialiased">
        {/* Top ribbon with countdown */}
        <div className="w-full bg-amber-600 text-black text-sm py-2">
          <div className="max-w-4xl mx-auto px-4 flex items-center justify-between">
            <div className="font-semibold">75% off for a limited time. Offer ends soon.</div>
            <div className="font-mono">{formatted()}</div>
          </div>
        </div>

        {/* Header */}
        <header className={`w-full top-0 z-40 ${showSticky ? "fixed bg-gray-900/95 shadow" : "absolute"}`}>
          <div className="max-w-5xl mx-auto px-4 py-4 flex items-center justify-between">
            <div className="text-lg font-bold">Crypto University</div>
            <button
              onClick={goToCheckout}
              className="bg-emerald-500 hover:bg-emerald-600 text-black font-semibold px-4 py-2 rounded-full shadow"
            >
              Get lifetime access — ${PRICE}
            </button>
          </div>
        </header>

        <main className="max-w-5xl mx-auto px-4 pt-28 pb-20">
          {/* Hero */}
          <section className="grid grid-cols-1 md:grid-cols-2 gap-8 items-start">
            <div>
              <div className="text-emerald-400 font-semibold mb-3">LIMITED TIME OFFER</div>
              <h1 className="text-2xl sm:text-3xl md:text-4xl font-extrabold leading-tight">
                PLUG THIS CRYPTO MASTERCLASS INTO YOUR TRADING TO INSTANTLY START FINDING
                HIGH-PROBABILITY SETUPS, AVOID BIG LOSSES, AND BUILD REAL WEALTH
              </h1>
              <p className="mt-4 text-gray-300">Works for any experience level, account size, or past track record.</p>

              <div className="mt-6">
                <button
                  onClick={goToCheckout}
                  className="bg-emerald-500 hover:bg-emerald-600 text-black font-bold px-6 py-3 rounded-full shadow-lg text-sm"
                >
                  Get lifetime access for ${PRICE}
                </button>
                <div className="mt-2 text-sm text-gray-300">One time payment. Lifetime access. 14 day money back guarantee.</div>
              </div>

              <div className="mt-6 flex flex-wrap gap-4 text-sm text-gray-200">
                <div className="flex-1">270K YouTube</div>
                <div className="flex-1">88K on X</div>
                <div className="flex-1">77K on Instagram</div>
                <div className="flex-1">10K in Telegram</div>
                <div className="w-full">40,000 plus students trained worldwide</div>
              </div>
            </div>

            <div>
              <div className="aspect-w-16 aspect-h-9 bg-black rounded-lg overflow-hidden relative">
                <img src="/og.jpg" alt="Hero video thumbnail" className="w-full h-full object-cover" />
                <button
                  onClick={() => sendEvent("view_content", { content_type: "video" })}
                  aria-label="Play video"
                  className="absolute inset-0 flex items-center justify-center text-white text-4xl"
                >
                  ►
                </button>
              </div>

              <p className="mt-3 text-gray-300 text-sm">The exact frameworks I use live on The Memecoin Show to pick entries, manage risk, and take profit.</p>

              <div className="mt-6 bg-gray-800 p-4 rounded-lg">
                <div className="text-3xl font-extrabold">${PRICE}</div>
                <div className="text-sm text-gray-300">Lifetime access</div>
                <button onClick={goToCheckout} className="mt-4 w-full bg-emerald-500 text-black font-bold py-2 rounded-full">Get lifetime access</button>
                <div className="mt-2 text-xs text-gray-400">14 day money back</div>
              </div>
            </div>
          </section>

          {/* What you get */}
          <section className="mt-12 bg-gray-850 p-6 rounded-lg">
            <h2 className="text-xl font-bold">What you get</h2>
            <ul className="mt-4 list-disc list-inside text-gray-200 space-y-2">
              <li>A step by step trading system for trend, momentum, and narrative plays</li>
              <li>Risk management methods to avoid blowups and protect profits</li>
              <li>How to find early winners before the crowd</li>
              <li>Entry, scaling, and take profit rules you can copy</li>
              <li>Real trade case studies from my streams</li>
            </ul>
            <div className="mt-6">
              <button onClick={goToCheckout} className="bg-emerald-500 px-5 py-3 rounded-full font-bold">Get lifetime access for ${PRICE}</button>
            </div>
          </section>

          {/* Modules & Bonuses */}
          <section className="mt-8 bg-gray-850 p-6 rounded-lg">
            <h2 className="text-xl font-bold">What is inside</h2>
            <ol className="mt-4 list-decimal list-inside text-gray-200 space-y-2">
              <li>Module 1: Fundamentals that actually matter in 2025</li>
              <li>Module 2: Finding coins and narratives with data</li>
              <li>Module 3: Entries and confirmation across timeframes</li>
              <li>Module 4: Risk, sizing, and drawdown control</li>
              <li>Module 5: Profit taking models that lock gains</li>
              <li>Module 6: Playbooks for memecoins, majors, and news events</li>
            </ol>

            <h3 className="mt-4 font-semibold">Bonuses</h3>
            <ul className="mt-2 text-gray-200 list-disc list-inside">
              <li>Indicator templates and checklists</li>
              <li>Private community access for Q and A</li>
              <li>Weekly office hours recording</li>
            </ul>

            <div className="mt-6">
              <button onClick={goToCheckout} className="bg-emerald-500 px-5 py-3 rounded-full font-bold">Get lifetime access for ${PRICE}</button>
            </div>
          </section>

          {/* Testimonials */}
          <section className="mt-8">
            <h2 className="text-xl font-bold">What students say</h2>
            <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="bg-gray-800 p-4 rounded-lg">
                  <div className="h-12 w-12 rounded-full bg-gray-700 mb-3" aria-hidden />
                  <div className="font-semibold">Student {i}</div>
                  <div className="text-sm text-gray-300 mt-2">“Short quote or praise that highlights results and clarity.”</div>
                </div>
              ))}
            </div>
          </section>

          {/* FAQ */}
          <section className="mt-8 bg-gray-850 p-6 rounded-lg">
            <h2 className="text-xl font-bold">FAQ</h2>
            <div className="mt-4 space-y-2">
              <details className="bg-gray-800 p-3 rounded">
                <summary className="font-semibold">How long do I keep access?</summary>
                <div className="mt-2 text-gray-300">Lifetime.</div>
              </details>
              <details className="bg-gray-800 p-3 rounded">
                <summary className="font-semibold">Do I need a big account?</summary>
                <div className="mt-2 text-gray-300">No. Rules scale from small to large.</div>
              </details>
              <details className="bg-gray-800 p-3 rounded">
                <summary className="font-semibold">What if I am a beginner?</summary>
                <div className="mt-2 text-gray-300">Start with Module 1 and follow the steps.</div>
              </details>
              <details className="bg-gray-800 p-3 rounded">
                <summary className="font-semibold">Refund policy?</summary>
                <div className="mt-2 text-gray-300">14 day no questions asked.</div>
              </details>
              <details className="bg-gray-800 p-3 rounded">
                <summary className="font-semibold">Is there live trading?</summary>
                <div className="mt-2 text-gray-300">The course is on demand. I trade live on The Memecoin Show.</div>
              </details>
            </div>
          </section>

          {/* Final CTA */}
          <section className="mt-10 text-center">
            <h3 className="text-2xl font-bold">Start now.</h3>
            <p className="text-gray-300 mt-2">Get lifetime access for ${PRICE}</p>
            <div className="mt-4">
              <button onClick={goToCheckout} className="bg-emerald-500 px-6 py-3 rounded-full font-bold">Start now — ${PRICE}</button>
            </div>
          </section>

          <footer className="mt-12 text-xs text-gray-500">
            <div>Legal · Privacy · Terms · Risk disclaimer</div>
          </footer>
        </main>
      </div>
    </>
  );
}
